import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArchiveBoxIcon, 
  ArrowUturnLeftIcon, 
  TrashIcon,
  XMarkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Plant } from '@/types';
import { manualArchiveService } from '@/services/manualArchiveService';
import { useAuth } from '@/hooks/useAuth';

interface ManualArchiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  onArchiveComplete?: () => void;
}

const ManualArchiveModal: React.FC<ManualArchiveModalProps> = ({
  isOpen,
  onClose,
  onArchiveComplete
}) => {
  const { user } = useAuth();
  const [archivedPlants, setArchivedPlants] = useState<Plant[]>([]);
  const [selectedPlants, setSelectedPlants] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalManuallyArchived: 0,
    archivedThisMonth: 0,
    archivedThisYear: 0
  });

  // Charger les plantes archivées manuellement
  useEffect(() => {
    if (isOpen && user) {
      loadArchivedPlants();
      loadStats();
    }
  }, [isOpen, user]);

  const loadArchivedPlants = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      const plants = await manualArchiveService.getManuallyArchivedPlants(user.uid);
      setArchivedPlants(plants);
    } catch (err) {
      setError('Impossible de charger les plantes archivées');
      console.error('Erreur lors du chargement des archives:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    if (!user) return;
    
    try {
      const archiveStats = await manualArchiveService.getManualArchiveStats(user.uid);
      setStats(archiveStats);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  const handleRestore = async () => {
    if (!user || selectedPlants.length === 0) return;
    
    try {
      setLoading(true);
      setError(null);
      await manualArchiveService.restorePlants(user.uid, selectedPlants);
      
      // Recharger les données
      await loadArchivedPlants();
      await loadStats();
      setSelectedPlants([]);
      
      // Notifier le parent que l'opération est terminée
      if (onArchiveComplete) {
        onArchiveComplete();
      }
    } catch (err) {
      setError('Impossible de restaurer les plantes sélectionnées');
      console.error('Erreur lors de la restauration:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePermanentDelete = async () => {
    if (!user || selectedPlants.length === 0) return;
    
    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir supprimer définitivement ${selectedPlants.length} plante(s) ? Cette action ne peut pas être annulée.`
    );
    
    if (!confirmed) return;
    
    try {
      setLoading(true);
      setError(null);
      await manualArchiveService.permanentlyDeletePlants(user.uid, selectedPlants);
      
      // Recharger les données
      await loadArchivedPlants();
      await loadStats();
      setSelectedPlants([]);
      
      // Notifier le parent que l'opération est terminée
      if (onArchiveComplete) {
        onArchiveComplete();
      }
    } catch (err) {
      setError('Impossible de supprimer les plantes sélectionnées');
      console.error('Erreur lors de la suppression:', err);
    } finally {
      setLoading(false);
    }
  };

  const togglePlantSelection = (plantId: string) => {
    setSelectedPlants(prev =>
      prev.includes(plantId)
        ? prev.filter(id => id !== plantId)
        : [...prev, plantId]
    );
  };

  const selectAll = () => {
    setSelectedPlants(archivedPlants.map(plant => plant.id));
  };

  const deselectAll = () => {
    setSelectedPlants([]);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, y: -20 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0.9, y: -20 }}
          className="bg-[#1c1a31] rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* En-tête */}
          <div className="p-6 border-b border-[#2a2847]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ArchiveBoxIcon className="w-6 h-6 text-[#d385f5]" />
                <h2 className="text-2xl font-bold text-white">Archives Manuelles</h2>
              </div>
              <Button
                onClick={onClose}
                variant="secondary"
                className="p-2"
              >
                <XMarkIcon className="w-5 h-5" />
              </Button>
            </div>
            
            {/* Statistiques */}
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-[#d385f5]">{stats.totalManuallyArchived}</div>
                <div className="text-sm text-gray-300">Total archivées</div>
              </div>
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{stats.archivedThisMonth}</div>
                <div className="text-sm text-gray-300">Ce mois</div>
              </div>
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">{stats.archivedThisYear}</div>
                <div className="text-sm text-gray-300">Cette année</div>
              </div>
            </div>
          </div>

          {/* Actions */}
          {archivedPlants.length > 0 && (
            <div className="p-4 border-b border-[#2a2847] bg-[#100f1c]">
              <div className="flex flex-wrap gap-2 items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    onClick={selectAll}
                    variant="secondary"
                    size="sm"
                    disabled={loading}
                  >
                    Tout sélectionner
                  </Button>
                  <Button
                    onClick={deselectAll}
                    variant="secondary"
                    size="sm"
                    disabled={loading}
                  >
                    Tout désélectionner
                  </Button>
                </div>
                
                {selectedPlants.length > 0 && (
                  <div className="flex gap-2">
                    <Button
                      onClick={handleRestore}
                      variant="secondary"
                      size="sm"
                      disabled={loading}
                      className="text-green-400 border-green-400 hover:bg-green-400/10"
                    >
                      <ArrowUturnLeftIcon className="w-4 h-4 mr-1" />
                      Restaurer ({selectedPlants.length})
                    </Button>
                    <Button
                      onClick={handlePermanentDelete}
                      variant="secondary"
                      size="sm"
                      disabled={loading}
                      className="text-red-400 border-red-400 hover:bg-red-400/10"
                    >
                      <TrashIcon className="w-4 h-4 mr-1" />
                      Supprimer ({selectedPlants.length})
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Contenu */}
          <div className="p-6 overflow-y-auto max-h-[60vh]" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            {error && (
              <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center gap-2">
                <InformationCircleIcon className="w-5 h-5 text-red-400" />
                <span className="text-red-400">{error}</span>
              </div>
            )}

            {loading ? (
              <div className="text-center py-8">
                <div className="text-white">Chargement des archives...</div>
              </div>
            ) : archivedPlants.length === 0 ? (
              <div className="text-center py-12">
                <ArchiveBoxIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Aucune plante archivée</h3>
                <p className="text-gray-300">Les plantes que vous archiverez manuellement apparaîtront ici.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {archivedPlants.map(plant => (
                  <Card
                    key={plant.id}
                    className={`cursor-pointer transition-all ${
                      selectedPlants.includes(plant.id)
                        ? 'ring-2 ring-[#d385f5] bg-[#d385f5]/10'
                        : 'hover:bg-[#2a2847]/50'
                    }`}
                    onClick={() => togglePlantSelection(plant.id)}
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-white truncate">{plant.name}</h4>
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          selectedPlants.includes(plant.id)
                            ? 'bg-[#d385f5] border-[#d385f5]'
                            : 'border-gray-400'
                        }`}>
                          {selectedPlants.includes(plant.id) && (
                            <div className="w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                      </div>
                      {plant.species && (
                        <p className="text-sm text-gray-300 mb-2">{plant.species}</p>
                      )}
                      <p className="text-xs text-gray-400">
                        Archivée le {plant.manuallyArchivedAt?.toDate().toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ManualArchiveModal;
