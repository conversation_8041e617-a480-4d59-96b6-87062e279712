import {
  collection,
  getDocs,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from './api';
import { Plant, DiagnosticRecord } from '@/types';

/**
 * Service d'export des données des plantes
 * Permet d'exporter les données en différents formats (CSV, JSON)
 */
class DataExportService {

  /**
   * Exporte les plantes au format CSV
   * @param userId - ID de l'utilisateur
   * @param includeArchived - Inclure les plantes archivées manuellement
   * @returns Contenu CSV sous forme de string
   */
  async exportPlantsToCSV(userId: string, includeArchived: boolean = false): Promise<string> {
    console.log(`📊 Export CSV des plantes pour l'utilisateur ${userId}`);
    
    try {
      const plants = await this.getPlants(userId, includeArchived);
      
      // En-têtes CSV
      const headers = [
        'Nom',
        'Espèce',
        'Description',
        'Date de création',
        'Archivée manuellement',
        'Date d\'archivage',
        'URL de l\'image'
      ];
      
      // Conversion des données
      const csvRows = [
        headers.join(','), // En-tête
        ...plants.map(plant => [
          this.escapeCsvValue(plant.name),
          this.escapeCsvValue(plant.species || ''),
          this.escapeCsvValue(plant.description || ''),
          plant.createdAt.toDate().toLocaleDateString('fr-FR'),
          plant.isManuallyArchived ? 'Oui' : 'Non',
          plant.manuallyArchivedAt ? plant.manuallyArchivedAt.toDate().toLocaleDateString('fr-FR') : '',
          this.escapeCsvValue(plant.coverImageUrl || '')
        ].join(','))
      ];
      
      console.log(`✅ Export CSV généré avec ${plants.length} plantes`);
      return csvRows.join('\n');
    } catch (error) {
      console.error('❌ Erreur lors de l\'export CSV:', error);
      throw new Error('Impossible d\'exporter les données au format CSV');
    }
  }

  /**
   * Exporte les plantes au format JSON
   * @param userId - ID de l'utilisateur
   * @param includeArchived - Inclure les plantes archivées manuellement
   * @param includeDiagnostics - Inclure les diagnostics
   * @returns Données JSON sous forme de string
   */
  async exportPlantsToJSON(
    userId: string, 
    includeArchived: boolean = false,
    includeDiagnostics: boolean = false
  ): Promise<string> {
    console.log(`📊 Export JSON des plantes pour l'utilisateur ${userId}`);
    
    try {
      const plants = await this.getPlants(userId, includeArchived);
      
      let exportData: any = {
        exportDate: new Date().toISOString(),
        totalPlants: plants.length,
        plants: plants.map(plant => ({
          id: plant.id,
          name: plant.name,
          species: plant.species,
          description: plant.description,
          createdAt: plant.createdAt.toDate().toISOString(),
          isManuallyArchived: plant.isManuallyArchived || false,
          manuallyArchivedAt: plant.manuallyArchivedAt?.toDate().toISOString() || null,
          coverImageUrl: plant.coverImageUrl
        }))
      };
      
      // Inclure les diagnostics si demandé
      if (includeDiagnostics) {
        const diagnostics = await this.getDiagnostics(userId);
        exportData.diagnostics = diagnostics.map(diagnostic => ({
          id: diagnostic.id,
          plantId: diagnostic.plantId,
          symptoms: diagnostic.symptoms,
          diagnosis: diagnostic.diagnosis,
          recommendations: diagnostic.recommendations,
          createdAt: diagnostic.createdAt.toDate().toISOString()
        }));
        exportData.totalDiagnostics = diagnostics.length;
      }
      
      console.log(`✅ Export JSON généré avec ${plants.length} plantes`);
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('❌ Erreur lors de l\'export JSON:', error);
      throw new Error('Impossible d\'exporter les données au format JSON');
    }
  }

  /**
   * Télécharge un fichier avec le contenu donné
   * @param content - Contenu du fichier
   * @param filename - Nom du fichier
   * @param mimeType - Type MIME du fichier
   */
  downloadFile(content: string, filename: string, mimeType: string): void {
    try {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Nettoyer l'URL
      URL.revokeObjectURL(url);
      
      console.log(`✅ Fichier téléchargé: ${filename}`);
    } catch (error) {
      console.error('❌ Erreur lors du téléchargement:', error);
      throw new Error('Impossible de télécharger le fichier');
    }
  }

  /**
   * Exporte et télécharge les plantes au format CSV
   * @param userId - ID de l'utilisateur
   * @param includeArchived - Inclure les plantes archivées
   */
  async exportAndDownloadCSV(userId: string, includeArchived: boolean = false): Promise<void> {
    try {
      const csvContent = await this.exportPlantsToCSV(userId, includeArchived);
      const filename = `mes_plantes_${new Date().toISOString().split('T')[0]}.csv`;
      this.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
    } catch (error) {
      console.error('❌ Erreur lors de l\'export CSV:', error);
      throw error;
    }
  }

  /**
   * Exporte et télécharge les plantes au format JSON
   * @param userId - ID de l'utilisateur
   * @param includeArchived - Inclure les plantes archivées
   * @param includeDiagnostics - Inclure les diagnostics
   */
  async exportAndDownloadJSON(
    userId: string, 
    includeArchived: boolean = false,
    includeDiagnostics: boolean = false
  ): Promise<void> {
    try {
      const jsonContent = await this.exportPlantsToJSON(userId, includeArchived, includeDiagnostics);
      const filename = `mes_plantes_${new Date().toISOString().split('T')[0]}.json`;
      this.downloadFile(jsonContent, filename, 'application/json;charset=utf-8;');
    } catch (error) {
      console.error('❌ Erreur lors de l\'export JSON:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les plantes de l'utilisateur
   * @param userId - ID de l'utilisateur
   * @param includeArchived - Inclure les plantes archivées manuellement
   * @returns Liste des plantes
   */
  private async getPlants(userId: string, includeArchived: boolean): Promise<Plant[]> {
    try {
      let plantsQuery = query(
        collection(db, 'users', userId, 'plants'),
        orderBy('createdAt', 'desc')
      );
      
      // Filtrer les plantes archivées si nécessaire
      if (!includeArchived) {
        plantsQuery = query(
          collection(db, 'users', userId, 'plants'),
          where('isManuallyArchived', '!=', true),
          orderBy('createdAt', 'desc')
        );
      }
      
      const snapshot = await getDocs(plantsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Plant));
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des plantes:', error);
      throw error;
    }
  }

  /**
   * Récupère tous les diagnostics de l'utilisateur
   * @param userId - ID de l'utilisateur
   * @returns Liste des diagnostics
   */
  private async getDiagnostics(userId: string): Promise<DiagnosticRecord[]> {
    try {
      const diagnosticsQuery = query(
        collection(db, 'users', userId, 'diagnostics'),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(diagnosticsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as DiagnosticRecord));
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des diagnostics:', error);
      return []; // Retourner un tableau vide en cas d'erreur
    }
  }

  /**
   * Échappe les valeurs pour le format CSV
   * @param value - Valeur à échapper
   * @returns Valeur échappée
   */
  private escapeCsvValue(value: string): string {
    if (!value) return '';
    
    // Échapper les guillemets et entourer de guillemets si nécessaire
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    
    return value;
  }

  /**
   * Obtient les statistiques d'export
   * @param userId - ID de l'utilisateur
   * @returns Statistiques des données exportables
   */
  async getExportStats(userId: string): Promise<{
    totalPlants: number;
    activePlants: number;
    archivedPlants: number;
    totalDiagnostics: number;
  }> {
    try {
      const allPlants = await this.getPlants(userId, true);
      const diagnostics = await this.getDiagnostics(userId);
      
      const archivedPlants = allPlants.filter(p => p.isManuallyArchived).length;
      const activePlants = allPlants.length - archivedPlants;
      
      return {
        totalPlants: allPlants.length,
        activePlants,
        archivedPlants,
        totalDiagnostics: diagnostics.length
      };
    } catch (error) {
      console.error('❌ Erreur lors du calcul des statistiques:', error);
      return {
        totalPlants: 0,
        activePlants: 0,
        archivedPlants: 0,
        totalDiagnostics: 0
      };
    }
  }
}

// Instance singleton du service
export const dataExportService = new DataExportService();
