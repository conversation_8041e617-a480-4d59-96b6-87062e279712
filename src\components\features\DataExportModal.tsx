import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowDownTrayIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  XMarkIcon,
  InformationCircleIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { dataExportService } from '@/services/dataExportService';
import { useAuth } from '@/hooks/useAuth';

interface DataExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DataExportModal: React.FC<DataExportModalProps> = ({
  isOpen,
  onClose
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalPlants: 0,
    activePlants: 0,
    archivedPlants: 0,
    totalDiagnostics: 0
  });
  
  // Options d'export
  const [includeArchived, setIncludeArchived] = useState(false);
  const [includeDiagnostics, setIncludeDiagnostics] = useState(false);

  // Charger les statistiques
  useEffect(() => {
    if (isOpen && user) {
      loadStats();
    }
  }, [isOpen, user]);

  const loadStats = async () => {
    if (!user) return;
    
    try {
      const exportStats = await dataExportService.getExportStats(user.uid);
      setStats(exportStats);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  const handleExportCSV = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      await dataExportService.exportAndDownloadCSV(user.uid, includeArchived);
      
      setSuccess('Export CSV téléchargé avec succès !');
    } catch (err) {
      setError('Impossible d\'exporter les données au format CSV');
      console.error('Erreur lors de l\'export CSV:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExportJSON = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      await dataExportService.exportAndDownloadJSON(user.uid, includeArchived, includeDiagnostics);
      
      setSuccess('Export JSON téléchargé avec succès !');
    } catch (err) {
      setError('Impossible d\'exporter les données au format JSON');
      console.error('Erreur lors de l\'export JSON:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, y: -20 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0.9, y: -20 }}
          className="bg-[#1c1a31] rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* En-tête */}
          <div className="p-6 border-b border-[#2a2847]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ArrowDownTrayIcon className="w-6 h-6 text-[#d385f5]" />
                <h2 className="text-2xl font-bold text-white">Export des Données</h2>
              </div>
              <Button
                onClick={onClose}
                variant="secondary"
                className="p-2"
              >
                <XMarkIcon className="w-5 h-5" />
              </Button>
            </div>
            
            {/* Statistiques */}
            <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-xl font-bold text-[#d385f5]">{stats.totalPlants}</div>
                <div className="text-xs text-gray-300">Total plantes</div>
              </div>
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-xl font-bold text-green-400">{stats.activePlants}</div>
                <div className="text-xs text-gray-300">Actives</div>
              </div>
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-xl font-bold text-gray-400">{stats.archivedPlants}</div>
                <div className="text-xs text-gray-300">Archivées</div>
              </div>
              <div className="bg-[#2a2847] rounded-lg p-3 text-center">
                <div className="text-xl font-bold text-blue-400">{stats.totalDiagnostics}</div>
                <div className="text-xs text-gray-300">Diagnostics</div>
              </div>
            </div>
          </div>

          {/* Contenu */}
          <div className="p-6 overflow-y-auto max-h-[60vh]" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            {/* Messages */}
            {error && (
              <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center gap-2">
                <InformationCircleIcon className="w-5 h-5 text-red-400" />
                <span className="text-red-400">{error}</span>
              </div>
            )}
            
            {success && (
              <div className="mb-4 p-4 bg-green-500/10 border border-green-500/20 rounded-lg flex items-center gap-2">
                <CheckIcon className="w-5 h-5 text-green-400" />
                <span className="text-green-400">{success}</span>
              </div>
            )}

            {/* Options d'export */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-4">Options d'export</h3>
              
              <div className="space-y-3">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={includeArchived}
                    onChange={(e) => setIncludeArchived(e.target.checked)}
                    className="w-4 h-4 text-[#d385f5] bg-[#2a2847] border-[#3D3B5E] rounded focus:ring-[#d385f5] focus:ring-2"
                  />
                  <span className="text-white">Inclure les plantes archivées manuellement</span>
                </label>
                
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={includeDiagnostics}
                    onChange={(e) => setIncludeDiagnostics(e.target.checked)}
                    className="w-4 h-4 text-[#d385f5] bg-[#2a2847] border-[#3D3B5E] rounded focus:ring-[#d385f5] focus:ring-2"
                  />
                  <span className="text-white">Inclure les diagnostics (JSON uniquement)</span>
                </label>
              </div>
            </div>

            {/* Formats d'export */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Formats disponibles</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Export CSV */}
                <Card className="p-4 hover:bg-[#2a2847]/50 transition-colors">
                  <div className="flex items-center gap-3 mb-3">
                    <DocumentTextIcon className="w-8 h-8 text-green-400" />
                    <div>
                      <h4 className="font-semibold text-white">Format CSV</h4>
                      <p className="text-sm text-gray-300">Tableur (Excel, Sheets)</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-400 mb-4">
                    Parfait pour l'analyse dans Excel ou Google Sheets. Contient les informations de base des plantes.
                  </p>
                  <Button
                    onClick={handleExportCSV}
                    disabled={loading}
                    className="w-full bg-green-500 hover:bg-green-600 text-white border-green-500"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                    {loading ? 'Export en cours...' : 'Télécharger CSV'}
                  </Button>
                </Card>

                {/* Export JSON */}
                <Card className="p-4 hover:bg-[#2a2847]/50 transition-colors">
                  <div className="flex items-center gap-3 mb-3">
                    <CodeBracketIcon className="w-8 h-8 text-blue-400" />
                    <div>
                      <h4 className="font-semibold text-white">Format JSON</h4>
                      <p className="text-sm text-gray-300">Données structurées</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-400 mb-4">
                    Format technique avec toutes les données. Peut inclure les diagnostics pour une sauvegarde complète.
                  </p>
                  <Button
                    onClick={handleExportJSON}
                    disabled={loading}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white border-blue-500"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                    {loading ? 'Export en cours...' : 'Télécharger JSON'}
                  </Button>
                </Card>
              </div>
            </div>

            {/* Informations */}
            <div className="mt-6 p-4 bg-[#2a2847]/50 rounded-lg">
              <div className="flex items-start gap-2">
                <InformationCircleIcon className="w-5 h-5 text-blue-400 mt-0.5" />
                <div className="text-sm text-gray-300">
                  <p className="font-semibold text-white mb-1">À propos de l'export</p>
                  <ul className="space-y-1 text-xs">
                    <li>• Les fichiers sont générés et téléchargés directement</li>
                    <li>• Aucune donnée n'est envoyée vers des serveurs externes</li>
                    <li>• Les exports incluent la date de création pour le suivi</li>
                    <li>• Format CSV : compatible avec tous les tableurs</li>
                    <li>• Format JSON : sauvegarde complète avec métadonnées</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default DataExportModal;
