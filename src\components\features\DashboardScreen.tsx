import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { getPlants, addPlant, getDiagnosticRecords } from '@/services/api';
import { manualArchiveService } from '@/services/manualArchiveService';
import { plantDuplicationService } from '@/services/plantDuplicationService';
import { Plant, DiagnosticRecord } from '@/types';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Spinner } from '@/components/common/Spinner';
import {
    PlusIcon,
    LeafIcon,
    CheckIcon,
    HeartIcon,
    ExclamationTriangleIcon,
    ArchiveBoxIcon,
    DocumentDuplicateIcon,
    ArrowsRightLeftIcon,
    TrashIcon,
    XMarkIcon
} from '@/components/common/icons';
import { getBackgroundConfig } from '@/utils/backgroundImages';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';
import ManualArchiveModal from './ManualArchiveModal';
import DataExportModal from './DataExportModal';

export type FilterType = 'all' | 'healthy' | 'needCare' | 'archived';

import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp } from 'firebase/firestore';

const AddPlantModal = ({ isOpen, onClose, onAdd }: { isOpen: boolean, onClose: () => void, onAdd: (name: string, species: string, description?: string) => void }) => {
    const [name, setName] = useState('');
    const [species, setSpecies] = useState('');
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        await onAdd(name, species, description);
        setIsLoading(false);
        setName('');
        setSpecies('');
        setDescription('');
        onClose();
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9, y: -20 }}
                    animate={{ scale: 1, y: 0 }}
                    exit={{ scale: 0.9, y: -20 }}
                    className="bg-[#1c1a31] p-8 rounded-2xl w-full max-w-md"
                    onClick={(e) => e.stopPropagation()}
                >
                    <h2 className="text-2xl font-bold text-white mb-6">Ajouter une Nouvelle Plante</h2>
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <label htmlFor="plant-name" className="block text-sm font-medium text-[#E0E0E0] mb-2">Nom de la Plante</label>
                            <input
                                type="text"
                                id="plant-name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                required
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Monstera Deliciosa"
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="plant-species" className="block text-sm font-medium text-[#E0E0E0] mb-2">Espèce (Optionnel)</label>
                            <input
                                type="text"
                                id="plant-species"
                                value={species}
                                onChange={(e) => setSpecies(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Araceae"
                            />
                        </div>
                        <div className="mb-6">
                            <label htmlFor="plant-description" className="block text-sm font-medium text-[#E0E0E0] mb-2">Description de l'état actuel</label>
                            <textarea
                                id="plant-description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none h-24 resize-none"
                                placeholder="Décrivez ce que vous observez sur votre plante : couleur des feuilles, état général, problèmes éventuels, etc."
                            />
                            <p className="text-xs text-gray-400 mt-1">
                                💡 Cette description aidera l'IA à mieux analyser votre plante lors des futurs diagnostics
                            </p>
                        </div>
                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="secondary" onClick={onClose}>Annuler</Button>
                            <Button type="submit" isLoading={isLoading}>Ajouter la Plante</Button>
                        </div>
                    </form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};


const DashboardScreen: React.FC = () => {
    const { user } = useAuth();
    const navigate = useNavigate();
    const [plants, setPlants] = useState<Plant[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [plantThumbnails, setPlantThumbnails] = useState<{ [plantId: string]: string }>({});
    const [plantDiagnostics, setPlantDiagnostics] = useState<{ [plantId: string]: DiagnosticRecord[] }>({});

    // États pour la gestion de la sélection
    const [isSelectionMode, setIsSelectionMode] = useState(false);
    const [selectedPlants, setSelectedPlants] = useState<string[]>([]);

    // État pour les filtres
    const [activeFilter, setActiveFilter] = useState<FilterType>('all');
    const [searchQuery, setSearchQuery] = useState('');

    // État pour la modal d'archivage manuel
    const [isManualArchiveModalOpen, setIsManualArchiveModalOpen] = useState(false);

    // État pour le groupement par espèce
    const [isGroupedBySpecies, setIsGroupedBySpecies] = useState(false);

    // État pour la modal d'export de données
    const [isDataExportModalOpen, setIsDataExportModalOpen] = useState(false);

    useEffect(() => {
        if (user) {
            const unsubscribe = getPlants(user.uid, (fetchedPlants) => {
                setPlants(fetchedPlants);
                setIsLoading(false);

                // Récupérer les vignettes et diagnostics pour chaque plante
                fetchedPlants.forEach(plant => {
                    getDiagnosticRecords(user.uid, plant.id, (diagnostics) => {
                        // Stocker les diagnostics pour l'analyse de santé
                        setPlantDiagnostics(prev => ({
                            ...prev,
                            [plant.id]: diagnostics
                        }));

                        // Récupérer la vignette du diagnostic le plus récent
                        if (diagnostics.length > 0 && diagnostics[0].imageUrls.length > 0) {
                            setPlantThumbnails(prev => ({
                                ...prev,
                                [plant.id]: diagnostics[0].imageUrls[0]
                            }));
                        }
                    });
                });
            });
            return () => unsubscribe();
        }
    }, [user]);

    const handleAddPlant = async (name: string, species: string, description?: string) => {
        if (user) {
            await addPlant(user.uid, {
                name,
                species,
                description: description || '',
                createdAt: serverTimestamp()
            });
        }
    };

    // Fonctions de gestion de la sélection
    const handleToggleSelectionMode = () => {
        setIsSelectionMode(!isSelectionMode);
        setSelectedPlants([]); // Réinitialiser la sélection
    };

    const handleSelectAll = () => {
        setSelectedPlants(plants.map(plant => plant.id));
    };

    const handleDeselectAll = () => {
        setSelectedPlants([]);
    };

    const handlePlantClick = (plantId: string) => {
        if (isSelectionMode) {
            setSelectedPlants(prev =>
                prev.includes(plantId)
                    ? prev.filter(id => id !== plantId)
                    : [...prev, plantId]
            );
        } else {
            navigate(`/plant/${plantId}`);
        }
    };

    // Actions de la barre d'outils
    const handleArchive = async (plantIds: string[]) => {
        if (!user || plantIds.length === 0) return;

        try {
            await manualArchiveService.archivePlants(user.uid, plantIds);

            // Réinitialiser la sélection et sortir du mode sélection
            setSelectedPlants([]);
            setIsSelectionMode(false);

            console.log(`✅ ${plantIds.length} plantes archivées avec succès`);
        } catch (error) {
            console.error('❌ Erreur lors de l\'archivage:', error);
            alert('Impossible d\'archiver les plantes sélectionnées');
        }
    };

    // Fonction pour gérer la fermeture de la modal d'archivage
    const handleManualArchiveComplete = () => {
        // Les données seront automatiquement mises à jour via l'écouteur en temps réel
        console.log('📦 Opération d\'archivage manuel terminée');
    };

    const handleDuplicate = async (plantIds: string[]) => {
        if (!user || plantIds.length === 0) return;

        try {
            const newPlantIds = await plantDuplicationService.duplicatePlants(user.uid, plantIds);

            // Réinitialiser la sélection et sortir du mode sélection
            setSelectedPlants([]);
            setIsSelectionMode(false);

            console.log(`✅ ${newPlantIds.length} plantes dupliquées avec succès`);

            // Afficher un message de confirmation
            if (newPlantIds.length > 0) {
                alert(`${newPlantIds.length} plante${newPlantIds.length > 1 ? 's' : ''} dupliquée${newPlantIds.length > 1 ? 's' : ''} avec succès !`);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la duplication:', error);
            alert('Impossible de dupliquer les plantes sélectionnées');
        }
    };

    const handleMove = (plantIds: string[]) => {
        console.log('Déplacer les plantes:', plantIds);
        // TODO: Implémenter le déplacement
    };

    const handleDelete = (plantIds: string[]) => {
        console.log('Supprimer les plantes:', plantIds);
        // TODO: Implémenter la suppression en lot
    };

    const handleExport = (plantIds: string[]) => {
        console.log('Ouvrir la modal d\'export pour les plantes:', plantIds);
        // Ouvrir la modal d'export de données
        setIsDataExportModalOpen(true);
    };

    // Fonctions de filtrage et comptage
    const getPlantHealthStatus = (plant: Plant): 'healthy' | 'needCare' | 'archived' => {
        // Si la plante est archivée manuellement
        if (plant.isManuallyArchived) return 'archived';

        // Récupérer les diagnostics de cette plante
        const diagnostics = plantDiagnostics[plant.id] || [];

        // Si aucun diagnostic, considérer comme ayant besoin de soins (pas encore évaluée)
        if (diagnostics.length === 0) {
            const daysSinceCreation = plant.createdAt
                ? Math.floor((Date.now() - plant.createdAt.toMillis()) / (1000 * 60 * 60 * 24))
                : 0;
            // Nouvelle plante (moins de 7 jours) = healthy, sinon needCare
            return daysSinceCreation <= 7 ? 'healthy' : 'needCare';
        }

        // Analyser les diagnostics récents (30 derniers jours)
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        const recentDiagnostics = diagnostics.filter(d => {
            const diagDate = d.timestamp.toDate();
            return diagDate >= thirtyDaysAgo;
        });

        // Si pas de diagnostic récent, considérer comme ayant besoin de soins
        if (recentDiagnostics.length === 0) {
            return 'needCare';
        }

        // Analyser le diagnostic le plus récent
        const latestDiagnosis = recentDiagnostics[0];

        // Si le dernier diagnostic indique une maladie, la plante a besoin de soins
        if (!latestDiagnosis.diagnosis.isHealthy) {
            return 'needCare';
        }

        // Vérifier s'il y a un traitement en cours
        if (latestDiagnosis.nextTreatmentDate) {
            const treatmentDate = latestDiagnosis.nextTreatmentDate.toDate();
            // Si le traitement est en retard, la plante a besoin de soins
            if (treatmentDate <= now) {
                return 'needCare';
            }
        }

        // Analyser la tendance des diagnostics récents
        const healthyCount = recentDiagnostics.filter(d => d.diagnosis.isHealthy).length;
        const totalCount = recentDiagnostics.length;
        const healthyRatio = healthyCount / totalCount;

        // Si moins de 70% des diagnostics récents sont sains, considérer comme ayant besoin de soins
        if (healthyRatio < 0.7) {
            return 'needCare';
        }

        return 'healthy';
    };

    const getFilteredPlants = () => {
        return plants.filter(plant => {
            const status = getPlantHealthStatus(plant);

            // Filtrage par statut
            let statusMatch = false;
            switch (activeFilter) {
                case 'healthy':
                    statusMatch = status === 'healthy';
                    break;
                case 'needCare':
                    statusMatch = status === 'needCare';
                    break;
                case 'archived':
                    statusMatch = status === 'archived';
                    break;
                case 'all':
                default:
                    statusMatch = !plant.archived; // Toutes sauf archivées par défaut
                    break;
            }

            // Filtrage par recherche textuelle
            let searchMatch = true;
            if (searchQuery.trim()) {
                const query = searchQuery.toLowerCase().trim();
                const nameMatch = plant.name.toLowerCase().includes(query);
                const speciesMatch = plant.species?.toLowerCase().includes(query) || false;
                const descriptionMatch = plant.description?.toLowerCase().includes(query) || false;
                searchMatch = nameMatch || speciesMatch || descriptionMatch;
            }

            return statusMatch && searchMatch;
        });
    };

    const getFilterCounts = () => {
        const healthy = plants.filter(p => getPlantHealthStatus(p) === 'healthy').length;
        const needCare = plants.filter(p => getPlantHealthStatus(p) === 'needCare').length;
        const archived = plants.filter(p => getPlantHealthStatus(p) === 'archived').length;
        const all = plants.filter(p => !p.isManuallyArchived).length; // Toutes les non-archivées manuellement

        return { all, healthy, needCare, archived };
    };

    // Fonction pour grouper les plantes par espèce
    const getGroupedPlants = (plantsToGroup: Plant[]) => {
        const grouped = plantsToGroup.reduce((acc, plant) => {
            const species = plant.species || 'Espèce non définie';
            if (!acc[species]) {
                acc[species] = [];
            }
            acc[species].push(plant);
            return acc;
        }, {} as Record<string, Plant[]>);

        // Trier les groupes par nom d'espèce
        const sortedGroups = Object.keys(grouped)
            .sort()
            .reduce((acc, species) => {
                acc[species] = grouped[species].sort((a, b) => a.name.localeCompare(b.name));
                return acc;
            }, {} as Record<string, Plant[]>);

        return sortedGroups;
    };

    const filteredPlants = getFilteredPlants();
    const filterCounts = getFilterCounts();

    if (isLoading) {
        return <div className="flex items-center justify-center h-screen"><Spinner size="lg" /></div>;
    }
    
    return (
        <BackgroundWrapper backgroundKey="dashboard" overlayOpacity={0.3}>
            <div className="p-4 sm:p-8 pt-20">
                <h1 className="text-4xl font-bold text-white mb-8 drop-shadow-lg">Mon Jardin</h1>

                {/* Tous les boutons sur une seule ligne */}
                <div className="mb-4 flex flex-wrap items-center gap-3">
                    {/* Barre de recherche */}
                    <div className="relative flex-1 min-w-[200px] max-w-md">
                        <input
                            type="text"
                            placeholder="Rechercher par nom, espèce..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full px-4 py-2 pl-10 bg-[#2a2847]/50 backdrop-blur-lg border border-[#3D3B5E] rounded-lg text-white placeholder-[#E0E0E0]/60 focus:outline-none focus:border-[#d385f5] focus:ring-1 focus:ring-[#d385f5] transition-all duration-200"
                        />
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg className="w-4 h-4 text-[#E0E0E0]/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        {searchQuery && (
                            <button
                                onClick={() => setSearchQuery('')}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-[#E0E0E0]/60 hover:text-white transition-colors"
                            >
                                <XMarkIcon className="w-4 h-4" />
                            </button>
                        )}
                    </div>

                    {/* Bouton Sélectionner */}
                    <Button
                        onClick={handleToggleSelectionMode}
                        variant={isSelectionMode ? "primary" : "secondary"}
                        className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2"
                    >
                        <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden xs:inline">{isSelectionMode ? 'Quitter sélection' : 'Sélectionner'}</span>
                        <span className="xs:hidden">Sélect.</span>
                    </Button>

                    {/* Filtres */}
                    <Button
                        onClick={() => setActiveFilter('all')}
                        variant={activeFilter === 'all' ? "primary" : "secondary"}
                        className={`flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 ${
                            activeFilter === 'all'
                                ? 'bg-[#d385f5] text-white border-[#d385f5]'
                                : 'text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10'
                        }`}
                    >
                        <LeafIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden xs:inline">Toutes</span>
                        <span className="xs:hidden">Tout</span>
                        <span className={`px-1 py-0.5 rounded-full text-xs font-bold ${
                            activeFilter === 'all' ? 'bg-white/20 text-white' : 'bg-[#d385f5]/10 text-[#d385f5]'
                        }`}>
                            {filterCounts.all}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('healthy')}
                        variant={activeFilter === 'healthy' ? "primary" : "secondary"}
                        className={`flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 ${
                            activeFilter === 'healthy'
                                ? 'bg-green-500 text-white border-green-500'
                                : 'text-green-400 border-green-400 hover:bg-green-400/10'
                        }`}
                    >
                        <HeartIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden sm:inline">En bonne santé</span>
                        <span className="sm:hidden">Santé</span>
                        <span className={`px-1 py-0.5 rounded-full text-xs font-bold ${
                            activeFilter === 'healthy' ? 'bg-white/20 text-white' : 'bg-green-400/10 text-green-400'
                        }`}>
                            {filterCounts.healthy}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('needCare')}
                        variant={activeFilter === 'needCare' ? "primary" : "secondary"}
                        className={`flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 ${
                            activeFilter === 'needCare'
                                ? 'bg-orange-500 text-white border-orange-500'
                                : 'text-orange-400 border-orange-400 hover:bg-orange-400/10'
                        }`}
                    >
                        <ExclamationTriangleIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden sm:inline">Nécessitent des soins</span>
                        <span className="sm:hidden">Soins</span>
                        <span className={`px-1 py-0.5 rounded-full text-xs font-bold ${
                            activeFilter === 'needCare' ? 'bg-white/20 text-white' : 'bg-orange-400/10 text-orange-400'
                        }`}>
                            {filterCounts.needCare}
                        </span>
                    </Button>

                    <Button
                        onClick={() => setActiveFilter('archived')}
                        variant={activeFilter === 'archived' ? "primary" : "secondary"}
                        className={`flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 ${
                            activeFilter === 'archived'
                                ? 'bg-gray-500 text-white border-gray-500'
                                : 'text-gray-400 border-gray-400 hover:bg-gray-400/10'
                        }`}
                    >
                        <ArchiveBoxIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden xs:inline">Archivées</span>
                        <span className="xs:hidden">Arch.</span>
                        <span className={`px-1 py-0.5 rounded-full text-xs font-bold ${
                            activeFilter === 'archived' ? 'bg-white/20 text-white' : 'bg-gray-400/10 text-gray-400'
                        }`}>
                            {filterCounts.archived}
                        </span>
                    </Button>

                    {/* Bouton pour accéder aux archives manuelles */}
                    <Button
                        onClick={() => setIsManualArchiveModalOpen(true)}
                        variant="secondary"
                        className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 text-purple-400 border-purple-400 hover:bg-purple-400/10"
                    >
                        <ArchiveBoxIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden sm:inline">Gérer Archives</span>
                        <span className="sm:hidden">Archives</span>
                    </Button>

                    {/* Bouton pour grouper par espèce */}
                    <Button
                        onClick={() => setIsGroupedBySpecies(!isGroupedBySpecies)}
                        variant="secondary"
                        className={`flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 ${
                            isGroupedBySpecies
                                ? 'text-blue-400 border-blue-400 bg-blue-400/10'
                                : 'text-blue-400 border-blue-400 hover:bg-blue-400/10'
                        }`}
                    >
                        <ArrowsRightLeftIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="hidden sm:inline">
                            {isGroupedBySpecies ? 'Vue normale' : 'Grouper par espèce'}
                        </span>
                        <span className="sm:hidden">
                            {isGroupedBySpecies ? 'Normal' : 'Grouper'}
                        </span>
                    </Button>
                </div>



                {/* Informations de sélection (si mode sélection actif) */}
                {isSelectionMode && (
                    <div className="mb-4 flex items-center gap-3">
                        <span className="text-[#E0E0E0] text-sm">
                            {selectedPlants.length} / {filteredPlants.length} sélectionnée{selectedPlants.length > 1 ? 's' : ''}
                        </span>

                        <Button
                            onClick={selectedPlants.length === filteredPlants.length ? handleDeselectAll : handleSelectAll}
                            variant="secondary"
                            className="text-xs px-2 py-1 sm:px-3"
                        >
                            <span className="hidden sm:inline">
                                {selectedPlants.length === filteredPlants.length ? 'Tout désélectionner' : 'Tout sélectionner'}
                            </span>
                            <span className="sm:hidden">
                                {selectedPlants.length === filteredPlants.length ? 'Désél. tout' : 'Sél. tout'}
                            </span>
                        </Button>
                    </div>
                )}

                {/* Actions de sélection (si mode sélection actif) */}
                {isSelectionMode && selectedPlants.length > 0 && (
                    <div className="mb-6 flex flex-wrap gap-1 sm:gap-2">
                        <Button
                            onClick={() => handleArchive(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10"
                        >
                            <ArchiveBoxIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="hidden xs:inline">Archiver ({selectedPlants.length})</span>
                            <span className="xs:hidden">Arch. ({selectedPlants.length})</span>
                        </Button>

                        <Button
                            onClick={() => handleDuplicate(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 text-blue-400 border-blue-400 hover:bg-blue-400/10"
                        >
                            <DocumentDuplicateIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="hidden xs:inline">Copier ({selectedPlants.length})</span>
                            <span className="xs:hidden">Cop. ({selectedPlants.length})</span>
                        </Button>

                        <Button
                            onClick={() => handleMove(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                        >
                            <ArrowsRightLeftIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="hidden xs:inline">Déplacer ({selectedPlants.length})</span>
                            <span className="xs:hidden">Dép. ({selectedPlants.length})</span>
                        </Button>

                        <Button
                            onClick={() => handleDelete(selectedPlants)}
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-1 text-xs sm:text-sm sm:px-3 sm:py-2 sm:gap-2 text-red-400 border-red-400 hover:bg-red-400/10"
                        >
                            <TrashIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="hidden xs:inline">Supprimer ({selectedPlants.length})</span>
                            <span className="xs:hidden">Supp. ({selectedPlants.length})</span>
                        </Button>
                    </div>
                )}

            <AnimatePresence>
                {isGroupedBySpecies ? (
                    // Affichage groupé par espèce
                    <div className="space-y-8">
                        {Object.entries(getGroupedPlants(filteredPlants)).map(([species, plantsInSpecies]) => (
                            <div key={species} className="space-y-4">
                                <h3 className="text-xl font-semibold text-white border-b border-[#3D3B5E] pb-2">
                                    {species} ({plantsInSpecies.length})
                                </h3>
                                <motion.div
                                    className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                                    initial="hidden"
                                    animate="visible"
                                    variants={{
                                        visible: { transition: { staggerChildren: 0.05 } }
                                    }}
                                >
                                    {plantsInSpecies.map(plant => {
                        const isSelected = selectedPlants.includes(plant.id);
                        return (
                        <Card
                            key={plant.id}
                            onClick={() => handlePlantClick(plant.id)}
                            className={`relative ${isSelectionMode ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-[#d385f5] bg-[#d385f5]/10' : ''}`}
                        >
                            {/* Indicateur de sélection */}
                            {isSelectionMode && (
                                <div className="absolute top-2 right-2 z-10">
                                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                                        isSelected
                                            ? 'bg-[#d385f5] border-[#d385f5]'
                                            : 'bg-transparent border-gray-400'
                                    }`}>
                                        {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
                                    </div>
                                </div>
                            )}

                            <div className="flex flex-col items-center text-center">
                                {plantThumbnails[plant.id] ? (
                                    <div className="w-20 h-20 mb-4 rounded-full overflow-hidden border-2 border-[#d385f5]/30">
                                        <img
                                            src={plantThumbnails[plant.id]}
                                            alt={plant.name}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                ) : (
                                    <div className="p-3 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                                        <img
                                            src={getBackgroundConfig('logo').url}
                                            alt="FloraSynth Logo"
                                            className="w-12 h-12 object-contain"
                                            onError={(e) => {
                                                // Fallback vers l'icône LeafIcon si l'image ne charge pas
                                                const target = e.target as HTMLImageElement;
                                                target.style.display = 'none';
                                                target.nextElementSibling?.classList.remove('hidden');
                                            }}
                                        />
                                        <LeafIcon className="w-12 h-12 text-[#d385f5] hidden" />
                                    </div>
                                )}
                                <h3 className="text-xl font-bold text-white">{plant.name}</h3>
                                {plant.species && <p className="text-sm text-[#E0E0E0]">{plant.species}</p>}
                            </div>
                        </Card>
                                        );
                                    })}
                                </motion.div>
                            </div>
                        ))}

                        {/* Bouton Ajouter une plante pour l'affichage groupé */}
                        <div className="mt-12 mb-16 px-4">
                            <motion.div
                                variants={{
                                    hidden: { opacity: 0, scale: 0.8 },
                                    visible: { opacity: 1, scale: 1 }
                                }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="bg-gradient-to-br from-[#1c1a31]/50 to-[#2a2847]/50 backdrop-blur-lg border-2 border-dashed border-[#d385f5]/50 rounded-2xl p-12 cursor-pointer hover:border-[#d385f5] transition-all duration-300 hover:shadow-lg hover:shadow-[#d385f5]/20 flex flex-col items-center justify-center text-center min-h-[240px] max-w-md mx-auto"
                                onClick={() => setIsModalOpen(true)}
                            >
                                <div className="p-4 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                                    <PlusIcon className="w-12 h-12 text-[#d385f5]" />
                                </div>
                                <h3 className="text-xl font-bold text-white mb-2">Ajouter une Plante</h3>
                                <p className="text-[#E0E0E0]">Cliquez ici pour ajouter une nouvelle plante à votre jardin</p>
                            </motion.div>
                        </div>
                    </div>
                ) : (
                    // Affichage normal (grille)
                    <motion.div
                        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                        initial="hidden"
                        animate="visible"
                        variants={{
                            visible: { transition: { staggerChildren: 0.1 } }
                        }}
                    >
                        {filteredPlants.map(plant => {
                            const isSelected = selectedPlants.includes(plant.id);
                            return (
                            <Card
                                key={plant.id}
                                onClick={() => handlePlantClick(plant.id)}
                                className={`relative ${isSelectionMode ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-[#d385f5] bg-[#d385f5]/10' : ''}`}
                            >
                                {/* Indicateur de sélection */}
                                {isSelectionMode && (
                                    <div className="absolute top-2 right-2 z-10">
                                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                                            isSelected
                                                ? 'bg-[#d385f5] border-[#d385f5]'
                                                : 'bg-transparent border-gray-400'
                                        }`}>
                                            {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
                                        </div>
                                    </div>
                                )}

                                <div className="flex flex-col items-center text-center">
                                    {plantThumbnails[plant.id] ? (
                                        <div className="w-20 h-20 mb-4 rounded-full overflow-hidden border-2 border-[#d385f5]/30">
                                            <img
                                                src={plantThumbnails[plant.id]}
                                                alt={plant.name}
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                    ) : (
                                        <div className="p-3 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                                            <img
                                                src={getBackgroundConfig('logo').url}
                                                alt="FloraSynth Logo"
                                                className="w-12 h-12 object-contain"
                                                onError={(e) => {
                                                    // Fallback vers l'icône LeafIcon si l'image ne charge pas
                                                    const target = e.target as HTMLImageElement;
                                                    target.style.display = 'none';
                                                    target.nextElementSibling?.classList.remove('hidden');
                                                }}
                                            />
                                            <LeafIcon className="w-12 h-12 text-[#d385f5] hidden" />
                                        </div>
                                    )}
                                    <h3 className="text-xl font-bold text-white">{plant.name}</h3>
                                    {plant.species && <p className="text-sm text-[#E0E0E0]">{plant.species}</p>}
                                </div>
                            </Card>
                            );
                        })}

                        {/* Bouton Ajouter une plante intégré dans la grille */}
                    <motion.div
                        variants={{
                            hidden: { opacity: 0, scale: 0.8 },
                            visible: { opacity: 1, scale: 1 }
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-gradient-to-br from-[#1c1a31]/50 to-[#2a2847]/50 backdrop-blur-lg border-2 border-dashed border-[#d385f5]/50 rounded-2xl p-8 cursor-pointer hover:border-[#d385f5] transition-all duration-300 hover:shadow-lg hover:shadow-[#d385f5]/20 flex flex-col items-center justify-center text-center min-h-[200px]"
                        onClick={() => setIsModalOpen(true)}
                    >
                        <div className="p-4 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                            <PlusIcon className="w-12 h-12 text-[#d385f5]" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Ajouter une Plante</h3>
                        <p className="text-sm text-[#E0E0E0]">Développez votre jardin</p>
                    </motion.div>
                    </motion.div>
                )}

                {/* Messages d'état */}
                {plants.length === 0 && (
                    <motion.div initial={{opacity: 0}} animate={{opacity: 1}} className="text-center py-12 mt-8">
                        <h2 className="text-2xl font-semibold text-white mb-4">Bienvenue dans votre jardin !</h2>
                        <p className="text-[#E0E0E0] mb-6">Commencez par ajouter votre première plante en cliquant sur la carte ci-dessus.</p>
                    </motion.div>
                )}

                {plants.length > 0 && filteredPlants.length === 0 && (
                    <motion.div initial={{opacity: 0}} animate={{opacity: 1}} className="text-center py-12 mt-8">
                        <h2 className="text-2xl font-semibold text-white mb-4">Aucune plante trouvée</h2>
                        {searchQuery ? (
                            <p className="text-[#E0E0E0] mb-6">
                                Aucune plante ne correspond à votre recherche "<span className="text-[#d385f5] font-semibold">{searchQuery}</span>".
                                <br />
                                Essayez avec d'autres mots-clés ou effacez la recherche.
                            </p>
                        ) : (
                            <p className="text-[#E0E0E0] mb-6">
                                Aucune plante ne correspond au filtre sélectionné.
                                <br />
                                Essayez de changer de filtre ou d'ajouter de nouvelles plantes.
                            </p>
                        )}
                    </motion.div>
                )}
            </AnimatePresence>


                <AddPlantModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    onAdd={handleAddPlant}
                />

                <ManualArchiveModal
                    isOpen={isManualArchiveModalOpen}
                    onClose={() => setIsManualArchiveModalOpen(false)}
                    onArchiveComplete={handleManualArchiveComplete}
                />

                <DataExportModal
                    isOpen={isDataExportModalOpen}
                    onClose={() => setIsDataExportModalOpen(false)}
                />
            </div>
        </BackgroundWrapper>
    );
};

export default DashboardScreen;