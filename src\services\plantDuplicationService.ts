import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  where,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './api';
import { Plant } from '@/types';

/**
 * Service de gestion de la duplication des plantes
 * Permet de dupliquer une ou plusieurs plantes avec leurs informations de base
 */
class PlantDuplicationService {

  /**
   * Duplique une seule plante
   * @param userId - ID de l'utilisateur
   * @param plantId - ID de la plante à dupliquer
   * @param customName - Nom personnalisé pour la copie (optionnel)
   * @returns ID de la nouvelle plante créée
   */
  async duplicatePlant(userId: string, plantId: string, customName?: string): Promise<string> {
    console.log(`🔄 Duplication de la plante ${plantId} pour l'utilisateur ${userId}`);
    
    try {
      // Récupérer la plante originale
      const originalPlant = await this.getPlantById(userId, plantId);
      if (!originalPlant) {
        throw new Error('Plante originale introuvable');
      }
      
      // Créer la copie avec un nouveau nom
      const duplicatedPlant: Omit<Plant, 'id'> = {
        name: customName || `${originalPlant.name} (Copie)`,
        species: originalPlant.species,
        description: originalPlant.description,
        coverImageUrl: originalPlant.coverImageUrl,
        createdAt: Timestamp.now(),
        // Ne pas copier les champs d'archivage
        isManuallyArchived: false,
        manuallyArchivedAt: undefined
      };
      
      // Ajouter la nouvelle plante à Firestore
      const plantsCollection = collection(db, 'users', userId, 'plants');
      const docRef = await addDoc(plantsCollection, duplicatedPlant);
      
      console.log(`✅ Plante dupliquée avec succès. Nouvel ID: ${docRef.id}`);
      return docRef.id;
    } catch (error) {
      console.error('❌ Erreur lors de la duplication de la plante:', error);
      throw new Error('Impossible de dupliquer la plante');
    }
  }

  /**
   * Duplique plusieurs plantes en lot
   * @param userId - ID de l'utilisateur
   * @param plantIds - Liste des IDs des plantes à dupliquer
   * @returns Liste des IDs des nouvelles plantes créées
   */
  async duplicatePlants(userId: string, plantIds: string[]): Promise<string[]> {
    console.log(`🔄 Duplication de ${plantIds.length} plantes pour l'utilisateur ${userId}`);
    
    try {
      const newPlantIds: string[] = [];
      
      // Traiter chaque plante individuellement pour éviter les conflits
      for (const plantId of plantIds) {
        try {
          const newPlantId = await this.duplicatePlant(userId, plantId);
          newPlantIds.push(newPlantId);
        } catch (error) {
          console.error(`❌ Erreur lors de la duplication de la plante ${plantId}:`, error);
          // Continuer avec les autres plantes même si une échoue
        }
      }
      
      console.log(`✅ ${newPlantIds.length}/${plantIds.length} plantes dupliquées avec succès`);
      return newPlantIds;
    } catch (error) {
      console.error('❌ Erreur lors de la duplication en lot:', error);
      throw new Error('Impossible de dupliquer les plantes sélectionnées');
    }
  }

  /**
   * Duplique plusieurs plantes avec des noms personnalisés
   * @param userId - ID de l'utilisateur
   * @param duplications - Liste des duplications avec noms personnalisés
   * @returns Liste des IDs des nouvelles plantes créées
   */
  async duplicatePlantsWithCustomNames(
    userId: string, 
    duplications: Array<{ plantId: string; customName: string }>
  ): Promise<string[]> {
    console.log(`🔄 Duplication personnalisée de ${duplications.length} plantes pour l'utilisateur ${userId}`);
    
    try {
      const newPlantIds: string[] = [];
      
      for (const { plantId, customName } of duplications) {
        try {
          const newPlantId = await this.duplicatePlant(userId, plantId, customName);
          newPlantIds.push(newPlantId);
        } catch (error) {
          console.error(`❌ Erreur lors de la duplication personnalisée de la plante ${plantId}:`, error);
        }
      }
      
      console.log(`✅ ${newPlantIds.length}/${duplications.length} plantes dupliquées avec noms personnalisés`);
      return newPlantIds;
    } catch (error) {
      console.error('❌ Erreur lors de la duplication personnalisée:', error);
      throw new Error('Impossible de dupliquer les plantes avec les noms personnalisés');
    }
  }

  /**
   * Récupère une plante par son ID
   * @param userId - ID de l'utilisateur
   * @param plantId - ID de la plante
   * @returns Plante trouvée ou null
   */
  private async getPlantById(userId: string, plantId: string): Promise<Plant | null> {
    try {
      const plantsQuery = query(
        collection(db, 'users', userId, 'plants'),
        where('__name__', '==', plantId)
      );
      
      const snapshot = await getDocs(plantsQuery);
      
      if (snapshot.empty) {
        return null;
      }
      
      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as Plant;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la plante:', error);
      return null;
    }
  }

  /**
   * Génère un nom unique pour une copie de plante
   * @param userId - ID de l'utilisateur
   * @param baseName - Nom de base de la plante
   * @returns Nom unique pour la copie
   */
  async generateUniqueCopyName(userId: string, baseName: string): Promise<string> {
    try {
      const plantsQuery = query(collection(db, 'users', userId, 'plants'));
      const snapshot = await getDocs(plantsQuery);
      
      const existingNames = snapshot.docs.map(doc => doc.data().name as string);
      
      let copyNumber = 1;
      let proposedName = `${baseName} (Copie)`;
      
      // Trouver un nom unique en incrémentant le numéro
      while (existingNames.includes(proposedName)) {
        copyNumber++;
        proposedName = `${baseName} (Copie ${copyNumber})`;
      }
      
      return proposedName;
    } catch (error) {
      console.error('❌ Erreur lors de la génération du nom unique:', error);
      return `${baseName} (Copie)`;
    }
  }

  /**
   * Obtient les statistiques de duplication
   * @param userId - ID de l'utilisateur
   * @returns Statistiques des duplications
   */
  async getDuplicationStats(userId: string): Promise<{
    totalPlants: number;
    copiesCount: number;
    originalPlantsCount: number;
  }> {
    try {
      const plantsQuery = query(collection(db, 'users', userId, 'plants'));
      const snapshot = await getDocs(plantsQuery);
      
      const plants = snapshot.docs.map(doc => doc.data().name as string);
      const copiesCount = plants.filter(name => 
        name.includes('(Copie)') || name.includes('(Copie ')
      ).length;
      
      return {
        totalPlants: plants.length,
        copiesCount,
        originalPlantsCount: plants.length - copiesCount
      };
    } catch (error) {
      console.error('❌ Erreur lors du calcul des statistiques:', error);
      return {
        totalPlants: 0,
        copiesCount: 0,
        originalPlantsCount: 0
      };
    }
  }
}

// Instance singleton du service
export const plantDuplicationService = new PlantDuplicationService();
