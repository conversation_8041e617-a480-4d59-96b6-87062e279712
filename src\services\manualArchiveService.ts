import {
  collection,
  doc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './api';
import { Plant } from '@/types';

/**
 * Service de gestion de l'archivage manuel des plantes
 * Distinct de l'archivage automatique annuel
 */
class ManualArchiveService {

  /**
   * Archive manuellement une liste de plantes
   * @param userId - ID de l'utilisateur
   * @param plantIds - Liste des IDs des plantes à archiver
   */
  async archivePlants(userId: string, plantIds: string[]): Promise<void> {
    console.log(`📦 Archivage manuel de ${plantIds.length} plantes pour l'utilisateur ${userId}`);
    
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();
      
      // Mettre à jour chaque plante pour l'archiver manuellement
      for (const plantId of plantIds) {
        const plantRef = doc(db, 'users', userId, 'plants', plantId);
        batch.update(plantRef, {
          isManuallyArchived: true,
          manuallyArchivedAt: now
        });
      }
      
      // Exécuter toutes les mises à jour en une seule transaction
      await batch.commit();
      
      console.log(`✅ ${plantIds.length} plantes archivées manuellement avec succès`);
    } catch (error) {
      console.error('❌ Erreur lors de l\'archivage manuel:', error);
      throw new Error('Impossible d\'archiver les plantes sélectionnées');
    }
  }

  /**
   * Restaure des plantes archivées manuellement
   * @param userId - ID de l'utilisateur
   * @param plantIds - Liste des IDs des plantes à restaurer
   */
  async restorePlants(userId: string, plantIds: string[]): Promise<void> {
    console.log(`📤 Restauration de ${plantIds.length} plantes pour l'utilisateur ${userId}`);
    
    try {
      const batch = writeBatch(db);
      
      // Mettre à jour chaque plante pour la restaurer
      for (const plantId of plantIds) {
        const plantRef = doc(db, 'users', userId, 'plants', plantId);
        batch.update(plantRef, {
          isManuallyArchived: false,
          manuallyArchivedAt: null
        });
      }
      
      // Exécuter toutes les mises à jour en une seule transaction
      await batch.commit();
      
      console.log(`✅ ${plantIds.length} plantes restaurées avec succès`);
    } catch (error) {
      console.error('❌ Erreur lors de la restauration:', error);
      throw new Error('Impossible de restaurer les plantes sélectionnées');
    }
  }

  /**
   * Récupère toutes les plantes archivées manuellement
   * @param userId - ID de l'utilisateur
   * @returns Liste des plantes archivées manuellement
   */
  async getManuallyArchivedPlants(userId: string): Promise<Plant[]> {
    try {
      const plantsQuery = query(
        collection(db, 'users', userId, 'plants'),
        where('isManuallyArchived', '==', true),
        orderBy('manuallyArchivedAt', 'desc')
      );
      
      const snapshot = await getDocs(plantsQuery);
      const archivedPlants = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Plant));
      
      console.log(`📋 ${archivedPlants.length} plantes archivées manuellement trouvées`);
      return archivedPlants;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des archives manuelles:', error);
      throw new Error('Impossible de récupérer les plantes archivées');
    }
  }

  /**
   * Supprime définitivement des plantes archivées manuellement
   * @param userId - ID de l'utilisateur
   * @param plantIds - Liste des IDs des plantes à supprimer définitivement
   */
  async permanentlyDeletePlants(userId: string, plantIds: string[]): Promise<void> {
    console.log(`🗑️ Suppression définitive de ${plantIds.length} plantes pour l'utilisateur ${userId}`);
    
    try {
      const batch = writeBatch(db);
      
      // Supprimer chaque plante définitivement
      for (const plantId of plantIds) {
        const plantRef = doc(db, 'users', userId, 'plants', plantId);
        batch.delete(plantRef);
        
        // TODO: Supprimer aussi les diagnostics associés
        // Cela nécessiterait une requête pour trouver tous les diagnostics de cette plante
      }
      
      // Exécuter toutes les suppressions en une seule transaction
      await batch.commit();
      
      console.log(`✅ ${plantIds.length} plantes supprimées définitivement`);
    } catch (error) {
      console.error('❌ Erreur lors de la suppression définitive:', error);
      throw new Error('Impossible de supprimer définitivement les plantes');
    }
  }

  /**
   * Obtient les statistiques des archives manuelles
   * @param userId - ID de l'utilisateur
   * @returns Statistiques des archives manuelles
   */
  async getManualArchiveStats(userId: string): Promise<{
    totalManuallyArchived: number;
    archivedThisMonth: number;
    archivedThisYear: number;
  }> {
    try {
      const archivedPlants = await this.getManuallyArchivedPlants(userId);
      
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      
      const archivedThisMonth = archivedPlants.filter(plant => 
        plant.manuallyArchivedAt && plant.manuallyArchivedAt.toDate() >= startOfMonth
      ).length;
      
      const archivedThisYear = archivedPlants.filter(plant => 
        plant.manuallyArchivedAt && plant.manuallyArchivedAt.toDate() >= startOfYear
      ).length;
      
      return {
        totalManuallyArchived: archivedPlants.length,
        archivedThisMonth,
        archivedThisYear
      };
    } catch (error) {
      console.error('❌ Erreur lors du calcul des statistiques:', error);
      return {
        totalManuallyArchived: 0,
        archivedThisMonth: 0,
        archivedThisYear: 0
      };
    }
  }
}

// Instance singleton du service
export const manualArchiveService = new ManualArchiveService();
